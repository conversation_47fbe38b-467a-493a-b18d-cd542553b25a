{"name": "e2e_financial_months", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app-tm", "sourceRoot": "apps/e2e_financial_months/src", "tags": [], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/e2e_financial_months", "index": "apps/e2e_financial_months/src/index.html", "main": "apps/e2e_financial_months/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/e2e_financial_months/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/e2e_financial_months/public"}], "styles": ["apps/e2e_financial_months/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/e2e_financial_months/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/e2e_financial_months/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4207, "publicHost": "http://localhost:4207"}, "configurations": {"production": {"buildTarget": "e2e_financial_months:build:production"}, "development": {"buildTarget": "e2e_financial_months:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "e2e_financial_months:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/e2e_financial_months/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "e2e_financial_months:build", "port": 4207, "watch": false}, "configurations": {"development": {"buildTarget": "e2e_financial_months:build:development"}, "production": {"buildTarget": "e2e_financial_months:build:production"}}}}}