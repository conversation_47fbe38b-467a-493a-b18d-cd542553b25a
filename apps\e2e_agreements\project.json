{"name": "e2e_agreements", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app-tm", "sourceRoot": "apps/e2e_agreements/src", "tags": [], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/e2e_agreements", "index": "apps/e2e_agreements/src/index.html", "main": "apps/e2e_agreements/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/e2e_agreements/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/e2e_agreements/public"}], "styles": ["apps/e2e_agreements/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/e2e_agreements/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/e2e_agreements/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4202, "publicHost": "http://localhost:4202"}, "configurations": {"production": {"buildTarget": "e2e_agreements:build:production"}, "development": {"buildTarget": "e2e_agreements:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "e2e_agreements:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/e2e_agreements/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "e2e_agreements:build", "port": 4202, "watch": false}, "configurations": {"development": {"buildTarget": "e2e_agreements:build:development"}, "production": {"buildTarget": "e2e_agreements:build:production"}}}}}