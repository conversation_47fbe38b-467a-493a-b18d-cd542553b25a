{"name": "e2e_borrowers", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app-tm", "sourceRoot": "apps/e2e_borrowers/src", "tags": [], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/e2e_borrowers", "index": "apps/e2e_borrowers/src/index.html", "main": "apps/e2e_borrowers/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/e2e_borrowers/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/e2e_borrowers/public"}], "styles": ["apps/e2e_borrowers/src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/e2e_borrowers/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/e2e_borrowers/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4204, "publicHost": "http://localhost:4204"}, "configurations": {"production": {"buildTarget": "e2e_borrowers:build:production"}, "development": {"buildTarget": "e2e_borrowers:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "e2e_borrowers:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/e2e_borrowers/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "e2e_borrowers:build", "port": 4204, "watch": false}, "configurations": {"development": {"buildTarget": "e2e_borrowers:build:development"}, "production": {"buildTarget": "e2e_borrowers:build:production"}}}}}